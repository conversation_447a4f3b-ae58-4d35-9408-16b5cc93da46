import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { TaskOrchestrator } from './TaskOrchestrator';
import { CognitiveProcessor } from './CognitiveProcessor';

export interface CortexConfig {
  neuralNetwork: NeuralNetworkManager;
  memory: CentralMemory;
  decisionEngine: DecisionEngine;
  communication: SynapticCommunication;
  io: any; // Socket.IO instance
}

export interface InstructionRequest {
  instructions: string;
  priority: 'low' | 'normal' | 'high' | 'critical';
  requester: string;
  timestamp: Date;
  context?: any;
}

export interface TaskStatus {
  taskId: string;
  status: 'pending' | 'analyzing' | 'planning' | 'executing' | 'completed' | 'failed';
  progress: number;
  assignedAgents: string[];
  startTime: Date;
  estimatedCompletion?: Date;
  result?: any;
  error?: string;
}

/**
 * Cortex Central - Orchestrateur Cognitif Principal
 * 
 * Le Cortex Central est le cerveau principal de l'organisme IA.
 * Il analyse les requêtes, prend des décisions stratégiques,
 * et orchestre les agents spécialisés pour accomplir les tâches.
 */
export class CortexCentral extends EventEmitter {
  private neuralNetwork: NeuralNetworkManager;
  private memory: CentralMemory;
  private decisionEngine: DecisionEngine;
  private communication: SynapticCommunication;
  private io: any;
  private taskOrchestrator: TaskOrchestrator;
  private cognitiveProcessor: CognitiveProcessor;
  
  private activeTasks: Map<string, TaskStatus> = new Map();
  private isInitialized: boolean = false;
  private startTime: Date;

  constructor(config: CortexConfig) {
    super();
    
    this.neuralNetwork = config.neuralNetwork;
    this.memory = config.memory;
    this.decisionEngine = config.decisionEngine;
    this.communication = config.communication;
    this.io = config.io;
    this.startTime = new Date();

    // Initialisation des processeurs cognitifs
    this.taskOrchestrator = new TaskOrchestrator({
      neuralNetwork: this.neuralNetwork,
      memory: this.memory,
      communication: this.communication
    });

    this.cognitiveProcessor = new CognitiveProcessor({
      memory: this.memory,
      decisionEngine: this.decisionEngine
    });
  }

  /**
   * Initialise le Cortex Central
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Cortex Central...');

      // Initialisation des composants cognitifs
      await this.taskOrchestrator.initialize();
      await this.cognitiveProcessor.initialize();

      // Configuration des événements de communication
      this.setupCommunicationEvents();

      // Configuration des événements neuronaux
      this.setupNeuralEvents();

      // Démarrage du monitoring cognitif
      this.startCognitiveMonitoring();

      this.isInitialized = true;
      logger.info('✅ Cortex Central initialisé avec succès');

      this.emit('cortex-initialized', {
        timestamp: new Date(),
        status: 'active'
      });

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du Cortex Central:', error);
      throw error;
    }
  }

  /**
   * Traite des instructions reçues de l'utilisateur ou d'autres systèmes
   */
  public async processInstructions(request: InstructionRequest): Promise<string> {
    const taskId = uuidv4();
    
    logger.info(`🎯 Nouvelle instruction reçue [${taskId}]:`, {
      instructions: request.instructions,
      priority: request.priority,
      requester: request.requester
    });

    // Création du statut de tâche initial
    const taskStatus: TaskStatus = {
      taskId,
      status: 'analyzing',
      progress: 0,
      assignedAgents: [],
      startTime: new Date()
    };

    this.activeTasks.set(taskId, taskStatus);

    try {
      // Phase 1: Analyse cognitive des instructions
      this.updateTaskStatus(taskId, 'analyzing', 10);
      const cognitiveAnalysis = await this.cognitiveProcessor.analyzeInstructions(request.instructions);

      // Phase 2: Prise de décision stratégique
      this.updateTaskStatus(taskId, 'planning', 25);
      const strategicPlan = await this.decisionEngine.createStrategicPlan({
        analysis: cognitiveAnalysis,
        priority: request.priority,
        context: request.context
      });

      // Phase 3: Orchestration des agents
      this.updateTaskStatus(taskId, 'executing', 40);
      const orchestrationResult = await this.taskOrchestrator.orchestrateTask({
        taskId,
        plan: strategicPlan,
        priority: request.priority
      });

      // Mise à jour des agents assignés
      taskStatus.assignedAgents = orchestrationResult.assignedAgents;
      this.updateTaskStatus(taskId, 'executing', 60);

      // Stockage en mémoire centrale
      await this.memory.storeTaskExecution({
        taskId,
        instructions: request.instructions,
        analysis: cognitiveAnalysis,
        plan: strategicPlan,
        orchestration: orchestrationResult
      });

      // Émission d'événements neuronaux
      this.emit('neural-signal', {
        type: 'task-initiated',
        taskId,
        priority: request.priority,
        assignedAgents: orchestrationResult.assignedAgents,
        timestamp: new Date()
      });

      this.emit('decision-made', {
        type: 'strategic-plan',
        taskId,
        plan: strategicPlan,
        timestamp: new Date()
      });

      logger.info(`✅ Tâche ${taskId} orchestrée avec succès`);
      return taskId;

    } catch (error) {
      logger.error(`❌ Erreur lors du traitement de la tâche ${taskId}:`, error);
      this.updateTaskStatus(taskId, 'failed', 100, error.message);
      throw error;
    }
  }

  /**
   * Récupère le statut d'une tâche
   */
  public async getTaskStatus(taskId: string): Promise<TaskStatus | null> {
    const status = this.activeTasks.get(taskId);
    
    if (!status) {
      // Tentative de récupération depuis la mémoire
      const storedTask = await this.memory.getTaskStatus(taskId);
      return storedTask;
    }

    return status;
  }

  /**
   * Met à jour le statut d'une tâche
   */
  private updateTaskStatus(
    taskId: string, 
    status: TaskStatus['status'], 
    progress: number, 
    error?: string
  ): void {
    const task = this.activeTasks.get(taskId);
    if (task) {
      task.status = status;
      task.progress = progress;
      if (error) task.error = error;
      
      // Estimation du temps de completion
      if (status === 'executing' && progress > 0) {
        const elapsed = Date.now() - task.startTime.getTime();
        const estimatedTotal = (elapsed / progress) * 100;
        task.estimatedCompletion = new Date(task.startTime.getTime() + estimatedTotal);
      }

      this.activeTasks.set(taskId, task);

      // Émission d'événement de mise à jour
      this.emit('task-updated', {
        taskId,
        status: task,
        timestamp: new Date()
      });
    }
  }

  /**
   * Configuration des événements de communication
   */
  private setupCommunicationEvents(): void {
    this.communication.on('agent-message', (data) => {
      this.handleAgentMessage(data);
    });

    this.communication.on('agent-status-update', (data) => {
      this.handleAgentStatusUpdate(data);
    });

    this.communication.on('task-completion', (data) => {
      this.handleTaskCompletion(data);
    });
  }

  /**
   * Configuration des événements neuronaux
   */
  private setupNeuralEvents(): void {
    this.neuralNetwork.on('agent-connected', (agentInfo) => {
      logger.info(`🔗 Agent connecté: ${agentInfo.id} (${agentInfo.type})`);
      this.emit('neural-signal', {
        type: 'agent-connected',
        agent: agentInfo,
        timestamp: new Date()
      });
    });

    this.neuralNetwork.on('agent-disconnected', (agentInfo) => {
      logger.warn(`❌ Agent déconnecté: ${agentInfo.id} (${agentInfo.type})`);
      this.emit('neural-signal', {
        type: 'agent-disconnected',
        agent: agentInfo,
        timestamp: new Date()
      });
    });

    this.neuralNetwork.on('synaptic-activity', (activity) => {
      this.emit('neural-signal', {
        type: 'synaptic-activity',
        activity,
        timestamp: new Date()
      });
    });
  }

  /**
   * Gestion des messages d'agents
   */
  private async handleAgentMessage(data: any): Promise<void> {
    logger.debug(`📨 Message reçu de l'agent ${data.agentId}:`, data.message);
    
    // Traitement cognitif du message
    const processedMessage = await this.cognitiveProcessor.processAgentMessage(data);
    
    // Mise à jour de la mémoire
    await this.memory.storeAgentInteraction(data.agentId, processedMessage);
    
    // Émission d'événement
    this.emit('agent-message-processed', {
      agentId: data.agentId,
      originalMessage: data.message,
      processedMessage,
      timestamp: new Date()
    });
  }

  /**
   * Gestion des mises à jour de statut d'agents
   */
  private handleAgentStatusUpdate(data: any): void {
    const { agentId, taskId, status, progress, result } = data;
    
    if (taskId && this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId)!;
      
      // Mise à jour du progrès global basé sur les agents
      const agentProgress = progress || 0;
      const totalAgents = task.assignedAgents.length;
      const baseProgress = 60; // Progrès de base après orchestration
      const agentContribution = (40 / totalAgents) * (agentProgress / 100);
      
      this.updateTaskStatus(taskId, 'executing', baseProgress + agentContribution);
    }

    this.emit('neural-signal', {
      type: 'agent-status-update',
      agentId,
      taskId,
      status,
      progress,
      timestamp: new Date()
    });
  }

  /**
   * Gestion de la completion de tâches
   */
  private async handleTaskCompletion(data: any): Promise<void> {
    const { taskId, agentId, result, success } = data;
    
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId)!;
      
      if (success) {
        task.result = result;
        this.updateTaskStatus(taskId, 'completed', 100);
        
        // Stockage du résultat en mémoire
        await this.memory.storeTaskResult(taskId, result);
        
        logger.info(`✅ Tâche ${taskId} complétée avec succès par l'agent ${agentId}`);
      } else {
        this.updateTaskStatus(taskId, 'failed', 100, result?.error || 'Échec de l\'agent');
        logger.error(`❌ Tâche ${taskId} échouée sur l'agent ${agentId}:`, result?.error);
      }

      // Nettoyage après un délai
      setTimeout(() => {
        this.activeTasks.delete(taskId);
      }, 300000); // 5 minutes
    }

    this.emit('task-completion', {
      taskId,
      agentId,
      success,
      result,
      timestamp: new Date()
    });
  }

  /**
   * Démarrage du monitoring cognitif
   */
  private startCognitiveMonitoring(): void {
    setInterval(() => {
      this.performCognitiveHealthCheck();
    }, 30000); // Toutes les 30 secondes
  }

  /**
   * Vérification de santé cognitive
   */
  private async performCognitiveHealthCheck(): Promise<void> {
    try {
      const healthMetrics = {
        activeTasks: this.activeTasks.size,
        connectedAgents: this.neuralNetwork.getConnectedAgents().length,
        memoryUsage: await this.memory.getUsageStats(),
        uptime: Date.now() - this.startTime.getTime(),
        timestamp: new Date()
      };

      this.emit('cognitive-health-check', healthMetrics);

      // Auto-optimisation si nécessaire
      if (healthMetrics.activeTasks > 100) {
        logger.warn('⚠️ Nombre élevé de tâches actives, optimisation en cours...');
        await this.optimizeTaskLoad();
      }

    } catch (error) {
      logger.error('❌ Erreur lors de la vérification de santé cognitive:', error);
    }
  }

  /**
   * Optimisation de la charge de tâches
   */
  private async optimizeTaskLoad(): Promise<void> {
    // Implémentation de l'optimisation de charge
    // Priorisation des tâches, redistribution, etc.
    logger.info('🔧 Optimisation de la charge de tâches en cours...');
  }

  /**
   * Récupère le statut du cortex
   */
  public getStatus(): any {
    return {
      isInitialized: this.isInitialized,
      activeTasks: this.activeTasks.size,
      uptime: Date.now() - this.startTime.getTime(),
      startTime: this.startTime,
      status: this.isInitialized ? 'active' : 'initializing'
    };
  }

  /**
   * Arrêt gracieux du cortex
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt du Cortex Central...');
    
    // Arrêt des composants
    await this.taskOrchestrator.shutdown();
    await this.cognitiveProcessor.shutdown();
    
    // Sauvegarde des tâches actives
    for (const [taskId, task] of this.activeTasks) {
      await this.memory.storeTaskStatus(taskId, task);
    }
    
    this.isInitialized = false;
    logger.info('✅ Cortex Central arrêté');
  }
}
