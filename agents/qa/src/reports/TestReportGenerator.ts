import { Logger } from 'winston';
import { TestResult, TestReport } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Générateur de Rapports de Tests
 * 
 * Génère des rapports HTML, JSON, XML et PDF
 * pour les résultats de tests.
 */
export class TestReportGenerator {
  private logger: Logger;
  private memory: WeaviateMemory;
  private reportsDirectory: string;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.reportsDirectory = process.env.REPORTS_DIR || './reports';
    
    fs.ensureDirSync(this.reportsDirectory);
  }

  /**
   * Génère tous les types de rapports
   */
  async generate(testResult: TestResult): Promise<TestReport[]> {
    this.logger.info('Génération des rapports de test', { id: testResult.id });

    const reports: TestReport[] = [];

    try {
      // 1. Rapport HTML
      const htmlReport = await this.generateHtmlReport(testResult);
      reports.push(htmlReport);

      // 2. Rapport JSON
      const jsonReport = await this.generateJsonReport(testResult);
      reports.push(jsonReport);

      // 3. Rapport XML (JUnit format)
      const xmlReport = await this.generateXmlReport(testResult);
      reports.push(xmlReport);

      // 4. Rapport CSV pour les métriques
      if (testResult.metrics && Object.keys(testResult.metrics).length > 0) {
        const csvReport = await this.generateCsvReport(testResult);
        reports.push(csvReport);
      }

      this.logger.info('Rapports générés avec succès', { 
        id: testResult.id,
        count: reports.length 
      });

      return reports;

    } catch (error) {
      this.logger.error('Erreur lors de la génération des rapports', { 
        id: testResult.id,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Génère un rapport HTML
   */
  private async generateHtmlReport(testResult: TestResult): Promise<TestReport> {
    const reportPath = path.join(this.reportsDirectory, `${testResult.id}-report.html`);

    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport de Test - ${testResult.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .status-passed { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .status-skipped { color: #ffc107; font-weight: bold; }
        .metric-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .test-suite { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
        .test-case { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .issues { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Rapport de Test QA</h1>
            <h2>Test ID: ${testResult.id}</h2>
            <p><strong>Type:</strong> ${testResult.type}</p>
            <p><strong>Statut:</strong> <span class="status-${testResult.status}">${testResult.status.toUpperCase()}</span></p>
            <p><strong>Durée:</strong> ${testResult.duration ? Math.round(testResult.duration / 1000) : 0}s</p>
            <p><strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
        </div>

        <div class="metric-card">
            <h3>📊 Résumé des Tests</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${testResult.summary.successRate * 100}%"></div>
            </div>
            <p><strong>Taux de succès:</strong> ${(testResult.summary.successRate * 100).toFixed(1)}%</p>
            <table>
                <tr>
                    <th>Total</th>
                    <th>Réussis</th>
                    <th>Échoués</th>
                    <th>Ignorés</th>
                    <th>Erreurs</th>
                </tr>
                <tr>
                    <td>${testResult.summary.total}</td>
                    <td class="status-passed">${testResult.summary.passed}</td>
                    <td class="status-failed">${testResult.summary.failed}</td>
                    <td class="status-skipped">${testResult.summary.skipped}</td>
                    <td class="status-failed">${testResult.summary.errors}</td>
                </tr>
            </table>
        </div>

        ${this.generateMetricsSection(testResult)}
        ${this.generateSuitesSection(testResult)}
        ${this.generateIssuesSection(testResult)}
        ${this.generateRecommendationsSection(testResult)}
        ${this.generateEnvironmentSection(testResult)}
    </div>
</body>
</html>`;

    await fs.writeFile(reportPath, html);

    return {
      type: 'html',
      name: 'Rapport HTML',
      path: reportPath,
      size: Buffer.byteLength(html),
      generatedAt: new Date()
    };
  }

  /**
   * Génère un rapport JSON
   */
  private async generateJsonReport(testResult: TestResult): Promise<TestReport> {
    const reportPath = path.join(this.reportsDirectory, `${testResult.id}-report.json`);
    const jsonContent = JSON.stringify(testResult, null, 2);

    await fs.writeFile(reportPath, jsonContent);

    return {
      type: 'json',
      name: 'Rapport JSON',
      path: reportPath,
      size: Buffer.byteLength(jsonContent),
      generatedAt: new Date()
    };
  }

  /**
   * Génère un rapport XML (format JUnit)
   */
  private async generateXmlReport(testResult: TestResult): Promise<TestReport> {
    const reportPath = path.join(this.reportsDirectory, `${testResult.id}-junit.xml`);

    let xml = `<?xml version="1.0" encoding="UTF-8"?>\n`;
    xml += `<testsuites name="${testResult.id}" tests="${testResult.summary.total}" failures="${testResult.summary.failed}" errors="${testResult.summary.errors}" time="${(testResult.duration || 0) / 1000}">\n`;

    for (const suite of testResult.details.suites) {
      xml += `  <testsuite name="${suite.name}" tests="${suite.tests.length}" failures="${suite.tests.filter(t => t.status === 'failed').length}" time="${suite.duration / 1000}">\n`;
      
      for (const test of suite.tests) {
        xml += `    <testcase name="${test.name}" time="${test.duration / 1000}"`;
        
        if (test.status === 'failed') {
          xml += `>\n`;
          xml += `      <failure message="${test.error?.message || 'Test failed'}">${test.error?.stack || ''}</failure>\n`;
          xml += `    </testcase>\n`;
        } else if (test.status === 'skipped') {
          xml += `>\n`;
          xml += `      <skipped/>\n`;
          xml += `    </testcase>\n`;
        } else {
          xml += `/>\n`;
        }
      }
      
      xml += `  </testsuite>\n`;
    }

    xml += `</testsuites>\n`;

    await fs.writeFile(reportPath, xml);

    return {
      type: 'xml',
      name: 'Rapport JUnit XML',
      path: reportPath,
      size: Buffer.byteLength(xml),
      generatedAt: new Date()
    };
  }

  /**
   * Génère un rapport CSV pour les métriques
   */
  private async generateCsvReport(testResult: TestResult): Promise<TestReport> {
    const reportPath = path.join(this.reportsDirectory, `${testResult.id}-metrics.csv`);

    let csv = 'Metric,Value,Unit\n';

    // Métriques de base
    csv += `Total Tests,${testResult.summary.total},count\n`;
    csv += `Passed Tests,${testResult.summary.passed},count\n`;
    csv += `Failed Tests,${testResult.summary.failed},count\n`;
    csv += `Success Rate,${(testResult.summary.successRate * 100).toFixed(2)},%\n`;
    csv += `Duration,${testResult.duration || 0},ms\n`;

    // Métriques spécifiques selon le type de test
    if (testResult.metrics.performance) {
      const perf = testResult.metrics.performance;
      if (perf.lighthouse) {
        csv += `Performance Score,${perf.lighthouse.performance},/100\n`;
        csv += `Accessibility Score,${perf.lighthouse.accessibility},/100\n`;
        csv += `Best Practices Score,${perf.lighthouse.bestPractices},/100\n`;
        csv += `SEO Score,${perf.lighthouse.seo},/100\n`;
      }
      if (perf.webVitals) {
        csv += `LCP,${perf.webVitals.lcp},ms\n`;
        csv += `FID,${perf.webVitals.fid},ms\n`;
        csv += `CLS,${perf.webVitals.cls},score\n`;
      }
    }

    if (testResult.metrics.accessibility) {
      csv += `Accessibility Score,${testResult.metrics.accessibility.score},/100\n`;
      csv += `Violations,${testResult.metrics.accessibility.violations.length},count\n`;
    }

    await fs.writeFile(reportPath, csv);

    return {
      type: 'csv',
      name: 'Métriques CSV',
      path: reportPath,
      size: Buffer.byteLength(csv),
      generatedAt: new Date()
    };
  }

  // Méthodes utilitaires pour générer les sections HTML

  private generateMetricsSection(testResult: TestResult): string {
    if (!testResult.metrics || Object.keys(testResult.metrics).length === 0) {
      return '';
    }

    let html = '<div class="metric-card"><h3>📈 Métriques Détaillées</h3>';

    // Métriques de performance
    if (testResult.metrics.performance) {
      html += '<h4>Performance</h4>';
      const perf = testResult.metrics.performance;
      
      if (perf.lighthouse) {
        html += `<p><strong>Score Lighthouse:</strong> ${perf.lighthouse.performance}/100</p>`;
        html += `<p><strong>Accessibilité:</strong> ${perf.lighthouse.accessibility}/100</p>`;
        html += `<p><strong>Bonnes Pratiques:</strong> ${perf.lighthouse.bestPractices}/100</p>`;
        html += `<p><strong>SEO:</strong> ${perf.lighthouse.seo}/100</p>`;
      }

      if (perf.webVitals) {
        html += `<p><strong>LCP:</strong> ${perf.webVitals.lcp}ms</p>`;
        html += `<p><strong>FID:</strong> ${perf.webVitals.fid}ms</p>`;
        html += `<p><strong>CLS:</strong> ${perf.webVitals.cls}</p>`;
      }
    }

    // Métriques d'accessibilité
    if (testResult.metrics.accessibility) {
      html += '<h4>Accessibilité</h4>';
      html += `<p><strong>Score:</strong> ${testResult.metrics.accessibility.score}/100</p>`;
      html += `<p><strong>Violations:</strong> ${testResult.metrics.accessibility.violations.length}</p>`;
    }

    html += '</div>';
    return html;
  }

  private generateSuitesSection(testResult: TestResult): string {
    let html = '<h3>🧪 Suites de Tests</h3>';

    for (const suite of testResult.details.suites) {
      html += `<div class="test-suite">`;
      html += `<h4>${suite.name} <span class="status-${suite.status}">(${suite.status})</span></h4>`;
      html += `<p><strong>Durée:</strong> ${Math.round(suite.duration / 1000)}s</p>`;

      for (const test of suite.tests) {
        html += `<div class="test-case">`;
        html += `<strong>${test.name}</strong> <span class="status-${test.status}">(${test.status})</span>`;
        html += `<br><small>Durée: ${test.duration}ms</small>`;
        
        if (test.error) {
          html += `<br><small style="color: #dc3545;">Erreur: ${test.error.message}</small>`;
        }
        
        html += `</div>`;
      }

      html += `</div>`;
    }

    return html;
  }

  private generateIssuesSection(testResult: TestResult): string {
    if (!testResult.issues || testResult.issues.length === 0) {
      return '';
    }

    let html = '<div class="issues"><h3>⚠️ Problèmes Identifiés</h3>';

    for (const issue of testResult.issues) {
      html += `<div style="margin: 10px 0; padding: 10px; border-left: 4px solid #ffc107; background: #fff3cd;">`;
      html += `<strong>${issue.title}</strong> <span style="color: #856404;">(${issue.severity})</span>`;
      html += `<br>${issue.description}`;
      if (issue.location) {
        html += `<br><small>Localisation: ${JSON.stringify(issue.location)}</small>`;
      }
      html += `</div>`;
    }

    html += '</div>';
    return html;
  }

  private generateRecommendationsSection(testResult: TestResult): string {
    if (!testResult.recommendations || testResult.recommendations.length === 0) {
      return '';
    }

    let html = '<div class="recommendations"><h3>💡 Recommandations</h3><ul>';

    for (const recommendation of testResult.recommendations) {
      html += `<li>${recommendation}</li>`;
    }

    html += '</ul></div>';
    return html;
  }

  private generateEnvironmentSection(testResult: TestResult): string {
    const env = testResult.details.environment;
    
    let html = '<div class="metric-card"><h3>🖥️ Environnement de Test</h3>';
    html += `<p><strong>OS:</strong> ${env.os}</p>`;
    html += `<p><strong>Node.js:</strong> ${env.node}</p>`;
    html += `<p><strong>Date:</strong> ${env.timestamp.toLocaleString('fr-FR')}</p>`;

    if (env.browser) {
      html += `<p><strong>Navigateur:</strong> ${env.browser.name} ${env.browser.version}</p>`;
      html += `<p><strong>Viewport:</strong> ${env.browser.viewport.width}x${env.browser.viewport.height}</p>`;
    }

    html += '</div>';
    return html;
  }
}
