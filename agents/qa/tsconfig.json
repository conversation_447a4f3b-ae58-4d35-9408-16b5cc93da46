{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types": ["src/types"], "@/core": ["src/core"], "@/runners": ["src/runners"], "@/analyzers": ["src/analyzers"], "@/reports": ["src/reports"], "@/memory": ["src/memory"], "@/communication": ["src/communication"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "coverage", "test-results", "reports", "logs", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}