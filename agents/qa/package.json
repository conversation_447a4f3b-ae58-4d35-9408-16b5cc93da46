{"name": "@retreat-and-be/agent-qa", "version": "1.0.0", "description": "Agent QA pour tests automatisés et assurance qualité", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:watch": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker:build": "docker build -t agent-qa .", "docker:run": "docker run -p 3008:3008 agent-qa", "sonar:scan": "sonar-scanner", "lighthouse:audit": "lighthouse-ci autorun"}, "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "express": "^4.18.0", "kafka-node": "^5.0.0", "redis": "^4.6.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "weaviate-ts-client": "^1.4.0", "uuid": "^9.0.0", "joi": "^17.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "node-cron": "^3.0.0", "jest": "^29.7.0", "playwright": "^1.40.0", "puppeteer": "^21.5.0", "lighthouse": "^11.3.0", "axe-core": "^4.8.0", "eslint": "^8.52.0", "sonarjs": "^1.0.0", "typescript": "^5.2.0", "ts-jest": "^29.1.0", "selenium-webdriver": "^4.15.0", "cypress": "^13.6.0", "codecov": "^3.8.0", "nyc": "^15.1.0", "mocha": "^10.2.0", "chai": "^4.3.0", "supertest": "^6.3.0", "jsdom": "^23.0.0", "cheerio": "^1.0.0", "pa11y": "^8.0.0", "webpagetest": "^0.4.0", "k6": "^0.47.0", "artillery": "^2.0.0", "newman": "^6.0.0", "postman-collection": "^4.2.0", "html-validate": "^8.7.0", "css-tree": "^2.3.0", "jshint": "^2.13.0", "stylelint": "^15.11.0", "prettier": "^3.1.0", "husky": "^8.0.0", "lint-staged": "^15.1.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/uuid": "^9.0.0", "@types/cors": "^2.8.0", "@types/jest": "^29.5.0", "@types/mocha": "^10.0.0", "@types/chai": "^4.3.0", "@types/supertest": "^2.0.0", "@types/selenium-webdriver": "^4.1.0", "@playwright/test": "^1.40.0", "ts-node": "^10.9.0", "nodemon": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-playwright": "^0.18.0"}, "keywords": ["ai-agent", "qa", "testing", "automation", "quality-assurance", "e2e-testing", "performance-testing", "accessibility-testing", "retreat-and-be", "living-ai"], "author": "Retreat And Be Team", "license": "MIT", "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}}