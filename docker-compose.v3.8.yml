version: '3.8'

services:
  # 🧠 CORTEX CENTRAL - Orchestrateur Cognitif
  cortex-central:
    build: ./cortex-central
    container_name: cortex-central
    ports:
      - "8080:8080"
    environment:
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis:6379
      - WEAVIATE_URL=weaviate:8080
      - NEURAL_NETWORK_MODE=active
      - DECISION_ENGINE=enabled
      - MEMORY_STORE=weaviate
      - SYNAPTIC_COMMUNICATION=kafka
    depends_on:
      - kafka
      - redis
      - weaviate
    volumes:
      - ./cortex-central/memory:/app/memory
      - ./cortex-central/logs:/app/logs
    networks:
      - neural-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🎯 AGENT FRONTEND - Spécialiste UI/UX
  agent-frontend:
    build: ./agents/frontend
    container_name: agent-frontend
    ports:
      - "3001:3001"
    environment:
      - MEMORY_STORE=pinecone-frontend
      - DOCS_PATH=/docs/frontend
      - CORTEX_CENTRAL_URL=http://cortex-central:8080
      - SPECIALIZATION=ui-ux
      - AGENT_TYPE=frontend
      - NEURAL_ID=agent-frontend-001
    volumes:
      - ./docs/frontend:/docs/frontend
      - ./agents/frontend/memory:/app/memory
      - ./agents/frontend/workspace:/app/workspace
    depends_on:
      - cortex-central
      - kafka
    networks:
      - neural-network
    restart: unless-stopped

  # ⚙️ AGENT BACKEND - Architecte API
  agent-backend:
    build: ./agents/backend
    container_name: agent-backend
    ports:
      - "3002:3002"
    environment:
      - MEMORY_STORE=pinecone-backend
      - SECURITY_MODULE=enabled
      - CORTEX_CENTRAL_URL=http://cortex-central:8080
      - SPECIALIZATION=api-architecture
      - AGENT_TYPE=backend
      - NEURAL_ID=agent-backend-001
    volumes:
      - ./docs/backend:/docs/backend
      - ./agents/backend/memory:/app/memory
      - ./agents/backend/workspace:/app/workspace
    depends_on:
      - cortex-central
      - kafka
    networks:
      - neural-network
    restart: unless-stopped

  # 🔧 AGENT DEVOPS - Automatiseur Infrastructure
  agent-devops:
    build: ./agents/devops
    container_name: agent-devops
    ports:
      - "3003:3003"
    environment:
      - MEMORY_STORE=pinecone-devops
      - INFRASTRUCTURE_MODE=kubernetes
      - CORTEX_CENTRAL_URL=http://cortex-central:8080
      - SPECIALIZATION=infrastructure
      - AGENT_TYPE=devops
      - NEURAL_ID=agent-devops-001
    volumes:
      - ./docs/devops:/docs/devops
      - ./agents/devops/memory:/app/memory
      - ./agents/devops/workspace:/app/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - cortex-central
      - kafka
    networks:
      - neural-network
    restart: unless-stopped

  # 🧪 AGENT QA - Testeur Intelligent
  agent-qa:
    build: ./agents/qa
    container_name: agent-qa
    ports:
      - "3004:3004"
    environment:
      - MEMORY_STORE=pinecone-qa
      - TESTING_FRAMEWORK=jest-cypress
      - CORTEX_CENTRAL_URL=http://cortex-central:8080
      - SPECIALIZATION=quality-assurance
      - AGENT_TYPE=qa
      - NEURAL_ID=agent-qa-001
    volumes:
      - ./docs/qa:/docs/qa
      - ./agents/qa/memory:/app/memory
      - ./agents/qa/workspace:/app/workspace
    depends_on:
      - cortex-central
      - kafka
    networks:
      - neural-network
    restart: unless-stopped

  # 💻 INTELLIGENT IDE - Interface Collaborative
  intelligent-ide:
    build: ./ide
    container_name: intelligent-ide
    ports:
      - "3000:3000"
    environment:
      - MONACO_CDN=enabled
      - COLLABORATION_WS=enabled
      - CORTEX_CENTRAL_URL=http://cortex-central:8080
      - WEBSOCKET_PORT=3000
      - REAL_TIME_COLLABORATION=enabled
    volumes:
      - ./ide/workspace:/app/workspace
      - ./ide/projects:/app/projects
    depends_on:
      - cortex-central
      - agent-frontend
      - agent-backend
      - agent-devops
      - agent-qa
    networks:
      - neural-network
    restart: unless-stopped

  # 📨 KAFKA - Système Nerveux Principal
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-neural
    ports:
      - "9092:9092"
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
    depends_on:
      - zookeeper
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - neural-network
    restart: unless-stopped

  # 🔗 ZOOKEEPER - Coordinateur Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-neural
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
    networks:
      - neural-network
    restart: unless-stopped

  # 🗄️ REDIS - Mémoire de Travail
  redis:
    image: redis:alpine
    container_name: redis-neural
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - neural-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🧠 WEAVIATE - Mémoire Vectorielle Centrale
  weaviate:
    image: semitechnologies/weaviate:latest
    container_name: weaviate-neural
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - neural-network
    restart: unless-stopped

volumes:
  kafka_data:
  zookeeper_data:
  redis_data:
  weaviate_data:

networks:
  neural-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
